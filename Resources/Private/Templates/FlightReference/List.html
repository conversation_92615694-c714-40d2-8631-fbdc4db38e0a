<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:layout name="Default" />

<f:section name="content">
    <div class="flight-reference-list">
        <h2>Available Flight Routes</h2>

        <!-- Search Filter -->
        <div class="flight-search-filter">
            <div class="search-group">
                <label for="route-search">Search Routes:</label>
                <div class="search-input-wrapper">
                    <input type="text" id="route-search" class="route-search-input" placeholder="Search by airport codes, city names, or route slugs...">
                    <button type="button" id="clear-search" class="clear-search-btn" title="Clear search" style="display: none;">×</button>
                </div>
                <div class="search-results-info">
                    <span class="results-count"></span>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <f:if condition="{showOriginFilter} || {showDestinationFilter}">
            <div class="flight-filters">
                <f:if condition="{showOriginFilter}">
                    <div class="filter-group">
                        <label for="origin-filter">Filter by Origin:</label>
                        <select id="origin-filter" class="origin-filter">
                            <option value="">All Origins</option>
                            <f:for each="{origins}" as="originName" key="originCode">
                                <option value="{originCode}">{originName} ({originCode})</option>
                            </f:for>
                        </select>
                    </div>
                </f:if>

                <f:if condition="{showDestinationFilter}">
                    <div class="filter-group">
                        <label for="destination-filter">Filter by Destination:</label>
                        <select id="destination-filter" class="destination-filter">
                            <option value="">All Destinations</option>
                            <f:for each="{destinations}" as="destinationName" key="destinationCode">
                                <option value="{destinationCode}">{destinationName} ({destinationCode})</option>
                            </f:for>
                        </select>
                    </div>
                </f:if>
            </div>
        </f:if>

        <!-- Routes Display -->
        <f:if condition="{routes}">
            <f:then>
                <f:switch expression="{displayMode}">
                    <f:case value="grid">
                        <div class="flight-routes-grid">
                            <f:for each="{routes}" as="route">
                                <div class="route-card"
                                     data-origin="{route.originCode}"
                                     data-destination="{route.destinationCode}"
                                     data-search-content="{route.originCode} {route.destinationCode} {route.originName} {route.destinationName} {route.routeSlug}">
                                    <div class="route-header">
                                        <h3>{route.originName} → {route.destinationName}</h3>
                                    </div>
                                    <div class="route-codes">
                                        <span class="origin-code">{route.originCode}</span>
                                        →
                                        <span class="destination-code">{route.destinationCode}</span>
                                    </div>
                                    <div class="route-types">
                                        <span class="origin-type">{route.originType}</span>
                                        to
                                        <span class="destination-type">{route.destinationType}</span>
                                    </div>
                                    <div class="route-action">
                                        <span class="route-info-text">Flight route available</span>
                                    </div>
                                </div>
                            </f:for>
                        </div>
                    </f:case>

                    <f:case value="cards">
                        <div class="flight-routes-cards">
                            <f:for each="{routes}" as="route">
                                <div class="route-card-large"
                                     data-origin="{route.originCode}"
                                     data-destination="{route.destinationCode}"
                                     data-search-content="{route.originCode} {route.destinationCode} {route.originName} {route.destinationName} {route.routeSlug}">
                                    <div class="card-body">
                                        <h4 class="card-title">{route.originName} to {route.destinationName}</h4>
                                        <p class="card-text">
                                            Fly from {route.originName} ({route.originCode})
                                            to {route.destinationName} ({route.destinationCode})
                                        </p>
                                        <span class="route-info-text">Flight route available</span>
                                    </div>
                                </div>
                            </f:for>
                        </div>
                    </f:case>

                    <f:defaultCase>
                        <!-- List view (default) -->
                        <div class="flight-routes-list">
                            <ul class="route-list">
                                <f:for each="{routes}" as="route">
                                    <li class="route-item"
                                        data-origin="{route.originCode}"
                                        data-destination="{route.destinationCode}"
                                        data-search-content="{route.originCode} {route.destinationCode} {route.originName} {route.destinationName} {route.routeSlug}">
                                        <div class="route-info">
                                            <span class="route-name">
                                                {route.originName} → {route.destinationName}
                                            </span>
                                            <span class="route-codes">
                                                ({route.originCode} → {route.destinationCode})
                                            </span>
                                        </div>
                                        <div class="route-info">
                                            <span class="route-info-text">Flight route available</span>
                                        </div>
                                    </li>
                                </f:for>
                            </ul>
                        </div>
                    </f:defaultCase>
                </f:switch>
            </f:then>
            <f:else>
                <div class="no-routes">
                    <p>No flight routes configured for this site.</p>
                </div>
            </f:else>
        </f:if>
    </div>
</f:section>
</html>
