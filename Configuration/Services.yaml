services:
  _defaults:
    autowire: true
    autoconfigure: true
    public: false

  Bgs\FlightLandingPages\:
    resource: '../Classes/*'
    exclude:
      - '../Classes/Domain/Model/*'
      - '../Classes/Routing/*'

  # Virtual Route Service
  Bgs\FlightLandingPages\Service\VirtualRouteService:
    public: false

  # Virtual Route Middleware (runs before PageResolver)
  Bgs\FlightLandingPages\Middleware\VirtualRouteHandler:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\FlightLandingPages\Service\VirtualRouteService'

  # Virtual Route Event Listeners
  Bgs\FlightLandingPages\EventListener\VirtualRouteDetectionListener:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\FlightLandingPages\Service\VirtualRouteService'
    tags:
      - name: event.listener
        identifier: 'flight-landing-pages-virtual-route-detection'
        event: TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent

  Bgs\FlightLandingPages\EventListener\VirtualPageReplacementListener:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\FlightLandingPages\Service\VirtualRouteService'
    tags:
      - name: event.listener
        identifier: 'flight-landing-pages-virtual-page-replacement'
        event: TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent

  Bgs\FlightLandingPages\EventListener\VirtualContentProcessingListener:
    public: false
    arguments:
      $virtualRouteService: '@Bgs\FlightLandingPages\Service\VirtualRouteService'
    tags:
      - name: event.listener
        identifier: 'flight-landing-pages-virtual-content-processing'
        event: TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent

  # Explicitly configure services that need special handling

  Bgs\FlightLandingPages\Service\SiteConfigurationService:
    arguments:
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'

  # Repository configuration - repositories are auto-configured by default
  Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository:
    public: true

  # Controller configuration
  Bgs\FlightLandingPages\Controller\DestinationsMenuController:
    public: true
    arguments:
      $flightRouteRepository: '@Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository'

  # URL Generation Service
  Bgs\FlightLandingPages\Service\UrlGenerationService:
    public: false
    arguments:
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'

  # Backend preview event listener
  Bgs\FlightLandingPages\EventListener\PagePreviewEventListener:
    public: false
    arguments:
      $flightRouteRepository: '@Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository'
      $siteFinder: '@TYPO3\CMS\Core\Site\SiteFinder'
      $urlGenerationService: '@Bgs\FlightLandingPages\Service\UrlGenerationService'
    tags:
      - name: event.listener
        identifier: 'flight-landing-pages-preview'
        event: TYPO3\CMS\Backend\Controller\Event\ModifyPageLayoutContentEvent

  # Backend CSV export controller
  Bgs\FlightLandingPages\Controller\Backend\CsvExportController:
    public: true
    arguments:
      $urlGenerationService: '@Bgs\FlightLandingPages\Service\UrlGenerationService'

  # Console commands
  Bgs\FlightLandingPages\Command\UpdateSlugCommand:
    tags:
      - name: 'console.command'
        command: 'flight:update-slugs'
