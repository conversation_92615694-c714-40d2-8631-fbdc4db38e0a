# Flight Landing Pages - Page TSconfig
# This configuration is automatically applied to pages with doktype 201 (Flight Landing Pages)

[page["doktype"] == 201]
    # Enable Destination Pairs Menu content element for Landing Pages
    TCEFORM.tt_content.CType.addItems {
        flightlandingpages_destinationpairsmenu = LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tt_content.CType.flightlandingpages_destinationpairsmenu
    }
[END]

[page["doktype"] != 201]
    # Hide Destination Pairs Menu content element on non-Landing Pages
    TCEFORM.tt_content.CType.removeItems := addToList(flightlandingpages_destinationpairsmenu)
[END]

# Configuration for Flight Landing Pages
[page["doktype"] == 201]
    # Configure template page field to only show Flight Template Pages (doktype 200)
    TCEFORM.pages.tx_flightlandingpages_template_page.PAGE_TSCONFIG_ID = 
    TCEFORM.pages.tx_flightlandingpages_template_page.addWhere = AND pages.doktype = 200
    
    # Set default cache lifetime
    TCAdefaults.pages.tx_flightlandingpages_cache_lifetime = 3600
    
    # Enable sitemap by default
    TCAdefaults.pages.tx_flightlandingpages_enable_sitemap = 1
[END]
