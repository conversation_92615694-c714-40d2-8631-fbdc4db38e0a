<?php
defined('TYPO3') or die();

call_user_func(static function () {
    // Register custom content element group for Flight Landing Pages
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItemGroup(
        'tt_content',
        'CType',
        'flight_landing_pages',
        'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tt_content.group.flight_landing_pages',
        'after:special'
    );

    // Register FlightReference plugin (for content elements)
    \TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
        'FlightLandingPages',
        'FlightReference',
        'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang.xlf:plugin.flight_reference.title',
        'mimetypes-x-content-list',
        'flight_landing_pages'
    );

    // Add FlexForm for FlightReference plugin
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
        '*',
        'FILE:EXT:flight_landing_pages/Configuration/FlexForms/FlightReference.xml',
        'flightlandingpages_flightreference'
    );

    // Add the Destination Pairs Menu content element to the "Type" dropdown
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
        'tt_content',
        'CType',
        [
            'label' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tt_content.CType.flightlandingpages_destinationpairsmenu',
            'value' => 'flightlandingpages_destinationpairsmenu',
            'icon' => 'content-destination-pairs-menu',
            'group' => 'flight_landing_pages',
            'description' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tt_content.CType.flightlandingpages_destinationpairsmenu.description',
        ],
        'header',
        'after'
    );

    // Configure the Destination Pairs Menu content element - similar to Header element
    $GLOBALS['TCA']['tt_content']['types']['flightlandingpages_destinationpairsmenu'] = [
        'showitem' => '
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:general,
                --palette--;;general,
                --palette--;;headers,
            --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.appearance,
                --palette--;;frames,
                --palette--;;appearanceLinks,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:language,
                --palette--;;language,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:access,
                --palette--;;hidden,
                --palette--;;access,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:categories,
                categories,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:notes,
                rowDescription,
            --div--;LLL:EXT:core/Resources/Private/Language/Form/locallang_tabs.xlf:extended,
        ',
        'columnsOverrides' => [
            'header' => [
                'description' => 'LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:tt_content.header.description.destinationpairsmenu',
            ],
        ],
        'previewRenderer' => \Bgs\FlightLandingPages\Preview\DestinationPairsMenuPreviewRenderer::class,
    ];

    // Content element is available on all pages but shows helpful warnings
    // in the backend preview when not used on Flight Landing Pages (doktype 201)
});
