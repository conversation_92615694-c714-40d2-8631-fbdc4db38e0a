<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\UserFunctions;

use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Content Element Restriction for Flight Landing Pages
 *
 * Restricts certain content elements to specific page types
 */
class ContentElementRestriction
{
    /**
     * Restrict content elements to Landing Pages only (doktype 201)
     *
     * This function is called by TCA itemsProcFunc to filter available
     * content element types based on the current page type.
     *
     * @param array $parameters TCA parameters
     */
    public function restrictToLandingPages(array &$parameters): void
    {
        // Get current page UID from various possible sources
        $pageUid = $this->getCurrentPageUid($parameters);
        
        if ($pageUid <= 0) {
            // If we can't determine the page, remove the restricted content element
            $this->removeRestrictedContentElement($parameters);
            return;
        }

        // Get page record to check doktype
        $page = BackendUtility::getRecord('pages', $pageUid, 'doktype');
        
        if (!$page || (int)$page['doktype'] !== 201) {
            // Not a Landing Page (doktype 201), remove the restricted content element
            $this->removeRestrictedContentElement($parameters);
        }
        
        // If it is a Landing Page (doktype 201), keep all items as they are
    }

    /**
     * Get current page UID from various sources
     *
     * @param array $parameters TCA parameters
     * @return int Page UID
     */
    protected function getCurrentPageUid(array $parameters): int
    {
        // Try to get page UID from different sources
        
        // 1. From the record being edited (for existing records)
        if (!empty($parameters['row']['pid'])) {
            return (int)$parameters['row']['pid'];
        }
        
        // 2. From GET/POST parameters (for new records)
        $pageUid = (int)($_GET['id'] ?? $_POST['id'] ?? 0);
        if ($pageUid > 0) {
            return $pageUid;
        }
        
        // 3. From edit parameters (when editing existing records)
        if (!empty($_GET['edit']['tt_content'])) {
            $editParams = $_GET['edit']['tt_content'];
            if (is_array($editParams)) {
                $contentUid = (int)array_key_first($editParams);
                if ($contentUid > 0) {
                    $contentRecord = BackendUtility::getRecord('tt_content', $contentUid, 'pid');
                    if ($contentRecord) {
                        return (int)$contentRecord['pid'];
                    }
                }
            }
        }
        
        return 0;
    }

    /**
     * Remove the restricted content element from available options
     *
     * @param array $parameters TCA parameters
     */
    protected function removeRestrictedContentElement(array &$parameters): void
    {
        $restrictedCType = 'flightlandingpages_destinationpairsmenu';
        
        // Filter out the restricted content element
        $parameters['items'] = array_filter(
            $parameters['items'],
            function ($item) use ($restrictedCType) {
                // Item structure: [label, value, icon, group, description]
                return isset($item['value']) ? $item['value'] !== $restrictedCType : true;
            }
        );
    }
}
